import modal
import subprocess

image = modal.Image.debian_slim().apt_install("ffmpeg").pip_install("fastapi[standard]")
app = modal.App("video_transcode", image=image, port=8000)

@app.asgi_app()
def fastapi_app():
    from fastapi import FastAPI, Request, HTTPException

    web_app = FastAPI()

    @web_app.post("/echo")
    async def echo(request: Request):
        body = await request.json()
        return body

    @web_app.post("/transcode")
    async def transcode(request: Request):
        try:
            body = await request.form()
            input_file = body["file"]
            output_file = body["output_file"]

            # FFmpeg command for 4K video transcoding
            ffmpeg_cmd = [
                "ffmpeg", "-i", input_file,
                "-vf", "scale=2160:3840:flags=lanczos",
                "-c:v", "libx264",
                "-crf", "13",
                "-preset", "slow",
                "-tune", "film",
                "-c:a", "aac",
                "-b:a", "192k",
                "-movflags", "+faststart",
                output_file
            ]

            result = subprocess.run(ffmpeg_cmd, capture_output=True, text=True)

            if result.returncode == 0:
                return {"status": "success", "message": "Video transcoded successfully", "output_file": output_file}
            else:
                raise HTTPException(status_code=500, detail=f"FFmpeg error: {result.stderr}")

        except KeyError as e:
            raise HTTPException(status_code=400, detail=f"Missing required field: {e}")
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Transcoding failed: {str(e)}")

    return web_app
