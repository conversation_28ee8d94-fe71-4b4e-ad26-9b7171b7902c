@echo off
setlocal enabledelayedexpansion

REM Loop through all .mp4 files that don't already have -4k in the name
for %%f in (*.mp4) do (
    echo Checking file: %%f

    echo %%f | findstr /i /C:"-4k" >nul
    if errorlevel 1 (
        set "filename=%%~nf"
        set "ext=%%~xf"
        set "output=!filename!-4k!ext!"
        echo Converting %%f to 2160x3840...
        ffmpeg -i "%%f" -vf scale=2160:3840:flags=lanczos -c:v libx264 -crf 13 -preset slow -tune film -c:a aac -b:a 192k -movflags +faststart "!output!"
        echo Done: !output!
    ) else (
        echo Skipping already converted file: %%f
    )
)

pause
